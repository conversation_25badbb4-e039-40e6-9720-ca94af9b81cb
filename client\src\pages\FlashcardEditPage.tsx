import React from "react";
import { useParams, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import AppLayout from "@/components/layout/AppLayout";
import { getDeck } from "@/lib/storage";
import FlashcardManager from "@/components/flashcards/FlashcardEditManager";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

const FlashcardEditPage: React.FC = () => {
  const params = useParams<{ deckId: string }>();
  const [, navigate] = useLocation();
  const deckId = params?.deckId;

  const { data: deck, isLoading } = useQuery({
    queryKey: [`/api/flashcard-decks/${deckId}`],
    queryFn: () => getDeck(deckId || ""),
    enabled: !!deckId,
  });

  const handleClose = () => {
    navigate("/");
  };

  if (isLoading) {
    return (
      <AppLayout title="Loading...">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-700 rounded w-1/3 mb-4"></div>
            <div className="h-64 bg-slate-700 rounded"></div>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!deck) {
    return (
      <AppLayout title="Deck Not Found">
        <div className="container mx-auto px-4 py-8">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2 text-purple-400">
                Deck Not Found
              </h3>
              <p className="text-slate-300 mb-4">
                The flashcard deck you're looking for doesn't exist.
              </p>
              <Button
                onClick={handleClose}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                Return to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout title={`Edit: ${deck.name}`}>
      <div className="container mx-auto px-4 py-8">
        <FlashcardManager
          selectedDeckId={deck.id}
          selectedDeckName={deck.name}
          onClose={handleClose}
        />
      </div>
    </AppLayout>
  );
};

export default FlashcardEditPage;
