import React, { useEffect, useState, useCallback } from "react";
import { supabase } from "../../lib/supabaseClient";
import { useAuth } from "../../hooks/useAuth";
import { Tables } from "../../types/supabase";
import { But<PERSON> } from "@/components/ui/button";
import Spinner from "@/components/ui/Spinner"; // Import Spinner
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"; // Using Card for consistency
import { useLocation } from "wouter";

type StudyDocument = Tables<"study_documents">;

interface DocumentListProps {
  onSelectDocument: (document: StudyDocument) => void;
  onGenerateQuiz: (document: StudyDocument) => void;
  onGenerateFlashcards: (document: StudyDocument) => void;
}

export const DocumentList: React.FC<DocumentListProps> = ({
  onSelectDocument,
  onGenerateQuiz,
  onGenerateFlashcards,
}) => {
  const { user } = useAuth();
  const [, navigate] = useLocation();
  const [documents, setDocuments] = useState<StudyDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDocuments = useCallback(async () => {
    if (!user) {
      setLoading(false);
      setDocuments([]); // Clear documents if no user
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const { data, error: dbError } = await supabase
        .from("study_documents")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (dbError) throw dbError;
      setDocuments(data || []);
    } catch (err: any) {
      console.error("Error fetching documents:", err);
      setError(err.message || "Failed to fetch documents.");
      setDocuments([]); // Clear documents on error
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchDocuments();

    if (!user) return;

    const channel = supabase
      .channel("study_documents_list_changes")
      .on(
        "postgres_changes",
        {
          event: "*", // Listen to all changes
          schema: "public",
          table: "study_documents",
          filter: `user_id=eq.${user.id}`,
        },
        (payload) => {
          console.log("Study document change received!", payload);
          // Smart update based on payload type
          if (payload.eventType === "INSERT") {
            setDocuments((prevDocs) => [
              payload.new as StudyDocument,
              ...prevDocs,
            ]);
          } else if (payload.eventType === "UPDATE") {
            setDocuments((prevDocs) =>
              prevDocs.map((doc) =>
                doc.id === payload.new.id ? (payload.new as StudyDocument) : doc
              )
            );
          } else if (payload.eventType === "DELETE") {
            setDocuments((prevDocs) =>
              prevDocs.filter((doc) => doc.id !== payload.old.id)
            );
          } else {
            fetchDocuments(); // Fallback to refetch for other event types or simplicity
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, fetchDocuments]);

  const handleDeleteDocument = async (
    documentId: string,
    filePath: string | null
  ) => {
    if (!user) return;
    if (
      !window.confirm(
        "Are you sure you want to delete this document? This may also delete associated flashcards and quizzes if database cascades are set up."
      )
    )
      return;

    try {
      const { error: dbDeleteError } = await supabase
        .from("study_documents")
        .delete()
        .match({ id: documentId, user_id: user.id });
      if (dbDeleteError)
        throw new Error(`DB delete error: ${dbDeleteError.message}`);

      // No need to manually remove from state if Supabase real-time DELETE event works.
      // setDocuments(documents.filter((doc) => doc.id !== documentId));

      if (filePath) {
        // Only attempt to delete from storage if filePath is valid
        const { error: storageDeleteError } = await supabase.storage
          .from("study_materials")
          .remove([filePath]);
        if (storageDeleteError) {
          console.error("Storage delete error:", storageDeleteError.message);
          alert(
            `Document record deleted, but failed to delete the file from storage: ${storageDeleteError.message}. Please check storage manually.`
          );
        } else {
          alert("Document and associated file deleted successfully.");
        }
      } else {
        alert(
          "Document record deleted successfully (no file path found in record)."
        );
      }
    } catch (err: any) {
      console.error("Error deleting document:", err);
      alert(`Failed to delete document: ${err.message}`);
    }
  };

  if (loading && documents.length === 0) {
    return (
      <div className="flex justify-center items-center py-10">
        <Spinner size="md" />
      </div>
    );
  }

  if (error) {
    return <p className="text-center text-destructive py-4">Error: {error}</p>;
  }

  if (documents.length === 0) {
    return (
      <div className="text-center py-10">
        <span className="material-icons text-6xl text-muted-foreground/50 mb-4 block">
          description
        </span>
        <p className="text-muted-foreground mb-2">No study documents found.</p>
        <p className="text-sm text-muted-foreground/80">
          Upload a document to get started.
        </p>
        {/* Optionally, add an upload button here if not handled by DocumentUploadForm above */}
      </div>
    );
  }

  return (
    // The Card component is used in DashboardPage.tsx which wraps this list.
    // This component will just render the list structure.
    <div className="mt-6">
      {/* The title "My Uploaded Documents" is now part of the parent CardHeader in DashboardPage */}
      <ul className="space-y-4">
        {documents.map((doc) => (
          <li
            key={doc.id}
            className="bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-150 ease-in-out"
          >
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
              <div className="flex-grow mb-3 sm:mb-0">
                <h4
                  className="font-semibold text-lg text-foreground hover:text-purple-400 cursor-pointer transition-colors"
                  onClick={() => navigate(`/documents/${doc.id}`)}
                  title="Click to open in full view"
                >
                  {doc.file_name || "Unnamed Document"}
                </h4>
                <div className="text-xs text-muted-foreground space-y-0.5 mt-1">
                  <p>Type: {doc.content_type || "N/A"}</p>
                  <p>
                    Size:{" "}
                    {doc.size_bytes
                      ? (doc.size_bytes / 1024).toFixed(2)
                      : "N/A"}{" "}
                    KB
                  </p>
                  <p>
                    Status:{" "}
                    <span
                      className={`font-medium ${
                        doc.status === "extracted"
                          ? "text-success"
                          : "text-warning"
                      }`}
                    >
                      {doc.status || "Pending"}
                    </span>
                  </p>
                  <p>
                    Uploaded: {new Date(doc.created_at!).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div className="flex flex-wrap gap-2 items-center justify-start sm:justify-end w-full sm:w-auto shrink-0">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onSelectDocument(doc)}
                  aria-label={`View ${doc.file_name}`}
                >
                  <span className="material-icons md-18 mr-1 sm:mr-0 md:mr-1">
                    visibility
                  </span>
                  <span className="hidden sm:inline">View</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/documents/${doc.id}`)}
                  aria-label={`Open ${doc.file_name} in full view`}
                  className="border-purple-400/30 text-purple-400 hover:bg-purple-400/10"
                >
                  <span className="material-icons md-18 mr-1 sm:mr-0 md:mr-1">
                    open_in_new
                  </span>
                  <span className="hidden sm:inline">Full View</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onGenerateQuiz(doc)}
                  disabled={doc.status !== "extracted"}
                  title={
                    doc.status !== "extracted"
                      ? "Text extraction pending or failed for quiz generation"
                      : "Generate Quiz"
                  }
                  aria-label={`Generate quiz for ${doc.file_name}`}
                >
                  <span className="material-icons md-18 mr-1 sm:mr-0 md:mr-1">
                    quiz
                  </span>
                  <span className="hidden sm:inline">Quiz</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onGenerateFlashcards(doc)}
                  disabled={doc.status !== "extracted"}
                  title={
                    doc.status !== "extracted"
                      ? "Text extraction pending or failed for flashcard generation"
                      : "Generate Flashcards"
                  }
                  aria-label={`Generate flashcards for ${doc.file_name}`}
                >
                  <span className="material-icons md-18 mr-1 sm:mr-0 md:mr-1">
                    style
                  </span>
                  <span className="hidden sm:inline">Cards</span>
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleDeleteDocument(doc.id, doc.file_path)}
                  aria-label={`Delete ${doc.file_name}`}
                >
                  <span className="material-icons md-18 mr-1 sm:mr-0 md:mr-1">
                    delete
                  </span>
                  <span className="hidden sm:inline">Delete</span>
                </Button>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};
